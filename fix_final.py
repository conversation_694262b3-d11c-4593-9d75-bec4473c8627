#!/usr/bin/env python3
import json
import shutil

def fix_json_structure():
    """Fix the JSON structure by removing all extra closing brackets"""
    
    # Restore from backup
    shutil.copy("postman-collection/Ecommerce-API-Collection.postman_collection.json.backup", 
                "postman-collection/Ecommerce-API-Collection.postman_collection.json")
    print("Restored from backup")
    
    with open("postman-collection/Ecommerce-API-Collection.postman_collection.json", 'r') as f:
        lines = f.readlines()
    
    # Remove the specific problematic lines that are just extra closing brackets
    lines_to_remove = []
    
    for i, line in enumerate(lines):
        line_num = i + 1
        stripped = line.strip()
        
        # These are the exact lines that have extra ] brackets
        if (line_num == 841 and stripped == ']') or \
           (line_num == 887 and stripped == ']') or \
           (line_num == 1053 and stripped == ']'):
            lines_to_remove.append(i)
            print(f"Marking line {line_num} for removal: {stripped}")
    
    # Remove in reverse order
    for i in reversed(lines_to_remove):
        del lines[i]
        print(f"Removed line {i+1}")
    
    # Write back
    content = ''.join(lines)
    with open("postman-collection/Ecommerce-API-Collection.postman_collection.json", 'w') as f:
        f.write(content)
    
    # Validate
    try:
        with open("postman-collection/Ecommerce-API-Collection.postman_collection.json", 'r') as f:
            json.load(f)
        print("✅ JSON is now valid!")
        return True
    except json.JSONDecodeError as e:
        print(f"❌ JSON still has errors: {e}")
        return False

if __name__ == "__main__":
    fix_json_structure()
