#!/usr/bin/env python3
import json
import re
import shutil

def fix_json_file(file_path):
    """Fix JSON syntax errors by analyzing structure and fixing bracket mismatches"""
    
    # Restore from backup first
    shutil.copy(f"{file_path}.backup", file_path)
    print("Restored from backup")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # Track bracket/brace nesting and identify issues
    stack = []
    fixed_lines = []
    
    for i, line in enumerate(lines):
        line_num = i + 1
        original_line = line
        stripped = line.strip()
        
        # Skip empty lines
        if not stripped:
            fixed_lines.append(line)
            continue
        
        # Analyze the line for brackets and braces
        line_chars = list(stripped)
        new_line_chars = []
        
        for j, char in enumerate(line_chars):
            if char in '[{':
                stack.append((char, line_num, j))
                new_line_chars.append(char)
            elif char in ']}':
                if stack:
                    opener, opener_line, opener_pos = stack.pop()
                    expected = ']' if opener == '[' else '}'
                    if char == expected:
                        new_line_chars.append(char)
                    else:
                        print(f"Warning: Mismatched bracket at line {line_num}, pos {j}. Expected {expected}, got {char}")
                        new_line_chars.append(expected)  # Fix the mismatch
                else:
                    # This is an unmatched closing bracket - check if it should be removed
                    if line_num in [841, 887, 1053] and stripped == ']':
                        print(f"Removing unmatched closing bracket at line {line_num}")
                        continue  # Skip this entire line
                    else:
                        print(f"Warning: Unmatched closing bracket at line {line_num}, pos {j}: {char}")
                        new_line_chars.append(char)
            else:
                new_line_chars.append(char)
        
        # Reconstruct the line with proper indentation
        if line_num in [841, 887, 1053] and stripped == ']':
            # Skip these problematic lines entirely
            print(f"Skipping problematic line {line_num}: {stripped}")
            continue
        
        # For lines that need item array closing, add it
        if line_num == 840 and stripped == '}':
            # This should be followed by ] to close the item array
            fixed_lines.append(line)
            # Add the missing ] with proper indentation
            indent = '\t' * 5  # Match the indentation level
            fixed_lines.append(f"{indent}]\n")
            continue
        elif line_num == 885 and stripped == '}':
            # This should be followed by ] to close the item array  
            fixed_lines.append(line)
            # Add the missing ] with proper indentation
            indent = '\t' * 5  # Match the indentation level
            fixed_lines.append(f"{indent}]\n")
            continue
        elif line_num == 1050 and stripped == '}':
            # This should be followed by ] to close the item array
            fixed_lines.append(line)
            # Add the missing ] with proper indentation
            indent = '\t' * 5  # Match the indentation level
            fixed_lines.append(f"{indent}]\n")
            continue
        
        # Reconstruct line with fixed characters
        if new_line_chars != line_chars:
            # Preserve original indentation
            indent = original_line[:len(original_line) - len(original_line.lstrip())]
            new_line = indent + ''.join(new_line_chars) + '\n'
            fixed_lines.append(new_line)
        else:
            fixed_lines.append(original_line)
    
    # Join back and write
    fixed_content = ''.join(fixed_lines)
    
    # Try to parse as JSON to validate
    try:
        json.loads(fixed_content)
        print("JSON is valid after fixes!")
        success = True
    except json.JSONDecodeError as e:
        print(f"JSON still has errors: {e}")
        success = False
    
    # Write the fixed content
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(fixed_content)
    
    return success

if __name__ == "__main__":
    fix_json_file("postman-collection/Ecommerce-API-Collection.postman_collection.json")
