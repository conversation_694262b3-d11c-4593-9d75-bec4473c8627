#!/usr/bin/env python3
import json
import re

def fix_postman_collection():
    """Fix all JSON syntax errors in the Postman collection"""
    
    file_path = "postman-collection/Ecommerce-API-Collection.postman_collection.json"
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Split into lines for easier processing
    lines = content.split('\n')
    
    # Track bracket/brace nesting
    stack = []
    fixed_lines = []
    i = 0
    
    while i < len(lines):
        line = lines[i]
        original_line = line
        stripped = line.strip()
        
        # Skip empty lines
        if not stripped:
            fixed_lines.append(line)
            i += 1
            continue
        
        # Get indentation
        indent = line[:len(line) - len(line.lstrip())]
        
        # Check for common patterns that need fixing
        
        # Pattern 1: Missing ] before },
        if stripped == '},' and i > 0:
            # Look back to see if we need to close an item array
            prev_lines = []
            j = i - 1
            while j >= 0 and len(prev_lines) < 10:
                prev_stripped = lines[j].strip()
                if prev_stripped:
                    prev_lines.append((j, prev_stripped))
                j -= 1
            
            # Check if we have an "item": [ that needs closing
            needs_item_close = False
            for line_num, prev_line in prev_lines:
                if '"item": [' in prev_line:
                    needs_item_close = True
                    break
                elif prev_line == ']':
                    needs_item_close = False
                    break
            
            if needs_item_close:
                # Add ] before },
                item_indent = indent + '\t'
                fixed_lines.append(f"{item_indent}]\n")
        
        # Pattern 2: Missing , after ]
        elif stripped == '}' and i < len(lines) - 1:
            next_stripped = lines[i + 1].strip() if i + 1 < len(lines) else ""
            if next_stripped.startswith('{'):
                # This should be },
                line = line.replace('}', '},')
        
        # Pattern 3: Extra ] that shouldn't be there
        elif stripped == ']' and i > 0 and i < len(lines) - 1:
            prev_stripped = lines[i - 1].strip() if i - 1 >= 0 else ""
            next_stripped = lines[i + 1].strip() if i + 1 < len(lines) else ""
            
            # If previous line already closes with ] and next line is },
            # then this ] is probably extra
            if prev_stripped == ']' and next_stripped in ['},', '}']:
                print(f"Removing extra ] at line {i + 1}")
                i += 1
                continue
        
        fixed_lines.append(line)
        i += 1
    
    # Join back
    fixed_content = '\n'.join(fixed_lines)
    
    # Write back
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(fixed_content)
    
    # Validate
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            json.load(f)
        print("✅ JSON is now valid!")
        return True
    except json.JSONDecodeError as e:
        print(f"❌ JSON still has errors: {e}")
        # Try to identify the line with error
        try:
            error_line = int(str(e).split('line ')[1].split(' ')[0])
            print(f"Error at line {error_line}: {lines[error_line-1] if error_line <= len(lines) else 'EOF'}")
        except:
            pass
        return False

if __name__ == "__main__":
    fix_postman_collection()
