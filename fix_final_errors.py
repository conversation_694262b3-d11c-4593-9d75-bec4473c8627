#!/usr/bin/env python3

def fix_specific_errors():
    """Fix the 2 specific errors identified by IDE diagnostics"""
    
    file_path = "postman-collection/Ecommerce-API-Collection.postman_collection.json"
    
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    print(f"File has {len(lines)} lines")
    
    # Error 1: Line 842 - Property expected (missing ] before },)
    # Look around line 842
    for i in range(835, min(850, len(lines))):
        line = lines[i]
        if line.strip() == '},':
            # Check if previous lines suggest we need to close an item array
            prev_lines = []
            for j in range(max(0, i-10), i):
                if lines[j].strip():
                    prev_lines.append(lines[j].strip())
            
            # If we see "item": [ in recent lines but no closing ], add it
            has_item_open = False
            has_item_close = False
            for prev_line in prev_lines:
                if '"item": [' in prev_line:
                    has_item_open = True
                if prev_line == ']':
                    has_item_close = True
                    break
            
            if has_item_open and not has_item_close:
                # Insert ] before },
                indent = line[:len(line) - len(line.lstrip())]
                lines.insert(i, f"{indent}\t]\n")
                print("Added ] before }, at line", i+1)
                break

    # Error 2: Line 1052 - End of file expected (missing comma after ])
    # Look around line 1052 (adjust for any insertions)
    for i in range(1045, min(1060, len(lines))):
        line = lines[i]
        if line.strip() == '},' and i < len(lines) - 1:
            next_line = lines[i+1].strip() if i+1 < len(lines) else ""
            if next_line.startswith('{'):
                # This looks correct, continue
                continue
        elif line.strip() == '}' and i < len(lines) - 1:
            next_line = lines[i+1].strip() if i+1 < len(lines) else ""
            if next_line.startswith('{'):
                # Missing comma
                lines[i] = line.rstrip() + ',\n'
                print("Added comma after } at line", i+1)
                break
    
    # Write back
    with open(file_path, 'w', encoding='utf-8') as f:
        f.writelines(lines)
    
    print(f"Fixed file has {len(lines)} lines")
    
    # Try to validate (with timeout)
    import json
    import signal
    
    def timeout_handler(signum, frame):
        raise TimeoutError("JSON parsing timed out")
    
    try:
        signal.signal(signal.SIGALRM, timeout_handler)
        signal.alarm(10)  # 10 second timeout
        
        with open(file_path, 'r', encoding='utf-8') as f:
            json.load(f)
        
        signal.alarm(0)  # Cancel timeout
        print("✅ JSON is valid!")
        return True
        
    except TimeoutError:
        print("⏰ JSON validation timed out (file too large)")
        return None
    except json.JSONDecodeError as e:
        signal.alarm(0)  # Cancel timeout
        print(f"❌ JSON still has errors: {e}")
        return False

if __name__ == "__main__":
    fix_specific_errors()
