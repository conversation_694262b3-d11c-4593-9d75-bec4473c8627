#!/usr/bin/env python3
import json
import re

def fix_postman_json():
    """Complete fix for Postman collection JSON syntax"""
    
    file_path = "postman-collection/Ecommerce-API-Collection.postman_collection.json"
    
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    print(f"Original file has {len(lines)} lines")
    
    # Find all problematic patterns and fix them
    fixed_lines = []
    i = 0
    
    while i < len(lines):
        line = lines[i]
        stripped = line.strip()
        
        # Skip empty lines
        if not stripped:
            fixed_lines.append(line)
            i += 1
            continue
        
        # Pattern: Look for folders that need item array closing
        if '"item": [' in line:
            # This starts an item array, we need to track when it should close
            fixed_lines.append(line)
            i += 1
            
            # Look ahead to find the matching closing
            bracket_count = 1
            brace_count = 0
            j = i
            
            while j < len(lines) and bracket_count > 0:
                next_line = lines[j]
                next_stripped = next_line.strip()
                
                # Count brackets and braces
                bracket_count += next_stripped.count('[') - next_stripped.count(']')
                brace_count += next_stripped.count('{') - next_stripped.count('}')
                
                fixed_lines.append(next_line)
                j += 1
                
                # If we hit a pattern like "}, {" and bracket_count is 1,
                # we probably need to close the item array
                if (bracket_count == 1 and 
                    next_stripped in ['},', '}'] and 
                    j < len(lines) and 
                    lines[j].strip().startswith('{')):
                    
                    # Insert ] before the },
                    last_line = fixed_lines.pop()
                    indent = last_line[:len(last_line) - len(last_line.lstrip())]
                    fixed_lines.append(f"{indent}\t]\n")
                    fixed_lines.append(last_line)
                    bracket_count = 0
                    break
            
            i = j
            continue
        
        # Pattern: Remove standalone ] that are extra
        elif stripped == ']':
            # Check context to see if this ] is needed
            prev_line = lines[i-1].strip() if i > 0 else ""
            next_line = lines[i+1].strip() if i < len(lines) - 1 else ""
            
            # If previous line is already ] and next is }, this ] is probably extra
            if prev_line == ']' and next_line in ['},', '}']:
                print(f"Removing extra ] at line {i+1}")
                i += 1
                continue
        
        # Pattern: Fix missing commas
        elif stripped == '}' and i < len(lines) - 1:
            next_line = lines[i+1].strip() if i < len(lines) - 1 else ""
            if next_line.startswith('{') and not line.rstrip().endswith(','):
                # Add comma
                line = line.rstrip() + ',\n'
        
        fixed_lines.append(line)
        i += 1
    
    # Write the fixed content
    fixed_content = ''.join(fixed_lines)
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(fixed_content)
    
    print(f"Fixed file has {len(fixed_lines)} lines")
    
    # Validate JSON
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            json.load(f)
        print("✅ JSON is now valid!")
        return True
    except json.JSONDecodeError as e:
        print(f"❌ JSON still has errors: {e}")
        return False

if __name__ == "__main__":
    fix_postman_json()
