#!/usr/bin/env python3
import json
import re

def fix_json_file(file_path):
    """Fix common JSON syntax errors in the Postman collection file"""

    # Restore from backup first
    import shutil
    shutil.copy(f"{file_path}.backup", file_path)
    print("Restored from backup")

    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()

    # Remove specific problematic lines that have extra closing brackets
    lines_to_remove = []

    for i, line in enumerate(lines):
        line_num = i + 1
        stripped = line.strip()

        # Check for lines that are just closing brackets that shouldn't be there
        # Based on jq error reports, these are the problematic lines
        if (line_num == 841 and stripped == ']') or \
           (line_num == 887 and stripped == ']') or \
           (line_num == 1053 and stripped == ']'):
            print(f"Marking line {line_num} for removal: extra closing bracket")
            lines_to_remove.append(i)

    # Remove lines in reverse order to maintain indices
    for i in reversed(lines_to_remove):
        print(f"Removing line {i+1}: {lines[i].strip()}")
        del lines[i]

    # Join back and write
    fixed_content = ''.join(lines)

    # Try to parse as JSON to validate
    try:
        json.loads(fixed_content)
        print("JSON is valid after fixes!")
        success = True
    except json.JSONDecodeError as e:
        print(f"JSON still has errors: {e}")
        # Try to identify the exact line with the error
        try:
            # Parse the error message to get line number
            error_str = str(e)
            if "line" in error_str:
                import re
                match = re.search(r'line (\d+)', error_str)
                if match:
                    error_line = int(match.group(1))
                    print(f"Error at line {error_line}: {lines[error_line-1].strip()}")
        except:
            pass
        success = False

    # Write the fixed content
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(fixed_content)

    return success

if __name__ == "__main__":
    fix_json_file("postman-collection/Ecommerce-API-Collection.postman_collection.json")
