#!/usr/bin/env python3
import re

def fix_json_with_regex():
    """Fix JSON using regex patterns"""
    
    file_path = "postman-collection/Ecommerce-API-Collection.postman_collection.json"
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("Applying regex fixes...")
    
    # Fix 1: Add missing ] before }, when there's an item array that needs closing
    # Pattern: }\n\t\t\t},\n\t\t\t{ (missing ] before },)
    pattern1 = r'(\t+)\}\n(\t+)\},\n(\t+)\{'
    def replace1(match):
        indent1, indent2, indent3 = match.groups()
        return f'{indent1}}}\n{indent2[:-1]}]\n{indent2}}},\n{indent3}{{'
    
    content = re.sub(pattern1, replace1, content)
    
    # Fix 2: Remove extra ] that appear after another ]
    # Pattern: ]\n\t\t\t\t]\n\t\t\t},
    pattern2 = r'\]\n(\t+)\]\n(\t+)\},'
    def replace2(match):
        indent1, indent2 = match.groups()
        return f']\n{indent2}}},'
    
    content = re.sub(pattern2, replace2, content)
    
    # Fix 3: Add missing commas after }
    # Pattern: }\n\t\t\t{ (missing comma)
    pattern3 = r'\}\n(\t+)\{'
    def replace3(match):
        indent = match.group(1)
        return f'}}},\n{indent}{{'
    
    content = re.sub(pattern3, replace3, content)
    
    # Write back
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("Regex fixes applied")
    
    # Quick validation
    import json
    try:
        json.loads(content)
        print("✅ JSON is now valid!")
        return True
    except json.JSONDecodeError as e:
        print(f"❌ JSON still has errors: {e}")
        return False

if __name__ == "__main__":
    fix_json_with_regex()
